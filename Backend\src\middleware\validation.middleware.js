const { body, validationResult } = require('express-validator');

const validate = (validations) => {
  return async (req, res, next) => {
    await Promise.all(validations.map(validation => validation.run(req)));

    const errors = validationResult(req);
    if (errors.isEmpty()) {
      return next();
    }

    return res.status(400).json({
      status: 'error',
      errors: errors.array()
    });
  };
};

const authValidation = {
  register: validate([
    // Common fields
    body('fullName')
      .trim()
      .notEmpty()
      .withMessage('Full name is required')
      .isLength({ min: 2 })
      .withMessage('Full name must be at least 2 characters long'),
    
    body('username')
      .trim()
      .notEmpty()
      .withMessage('Username is required')
      .matches(/^@/)
      .withMessage('Username must start with @')
      .isLength({ min: 3 })
      .withMessage('Username must be at least 3 characters long'),
    
    body('email')
      .trim()
      .notEmpty()
      .withMessage('Email is required')
      .isEmail()
      .withMessage('Please enter a valid email address'),
    
    body('password')
      .trim()
      .notEmpty()
      .withMessage('Password is required')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters long'),
    
    body('phoneNumber')
      .trim()
      .notEmpty()
      .withMessage('Phone number is required')
      .matches(/^\+?[1-9]\d{1,14}$/)
      .withMessage('Please enter a valid phone number'),
    
    body('role')
      .isInt({ min: 0, max: 4 })
      .withMessage('Invalid role specified'),
    
    // Provider specific fields
    body('licensedState')
      .if(body('role').equals(3))
      .notEmpty()
      .withMessage('Licensed state is required for providers'),
    
    body('affiliatedOrganization')
      .if(body('role').equals(3))
      .notEmpty()
      .withMessage('Affiliated organization is required for providers'),
    
    // Patient specific fields
    body('birthday')
      .if(body('role').equals(4))
      .notEmpty()
      .withMessage('Birthday is required for patients')
      .isISO8601()
      .withMessage('Please enter a valid date'),
    
    body('militaryVeteran')
      .if(body('role').equals(4))
      .isBoolean()
      .withMessage('Military veteran status must be a boolean value')
  ]),

  login: validate([
    body('email')
      .trim()
      .notEmpty()
      .withMessage('Email is required')
      .isEmail()
      .withMessage('Please enter a valid email address'),
    
    body('password')
      .trim()
      .notEmpty()
      .withMessage('Password is required')
  ])
};

module.exports = {
  authValidation
}; 