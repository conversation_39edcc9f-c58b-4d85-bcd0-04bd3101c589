"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Eye, EyeOff, ChevronDown } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useDispatch } from "react-redux";
import { setUser } from "@/store/userSlice";
import { authService } from "@/services/api";

export default function ProviderSignupPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    fullName: "",
    username: "",
    licensedState: "",
    affiliatedOrganization: "",
    email: "",
    phoneNumber: "",
    password: "",
    rememberMe: false,
    signUpForUpdates: true,
  });
  const dispatch = useDispatch();

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData({
      ...formData,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    console.log(formData, "formData");

    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Ensure username starts with @
      const username = formData.username.startsWith("@")
        ? formData.username
        : `@${formData.username}`;

      const response = await authService.registerProvider({
        ...formData,
        username,
        role: 3, // Provider role
      });

      // Store user data in Redux
      dispatch(setUser(response.data.user));

      // Redirect to provider dashboard
      router.push("/dashboard");
    } catch (error: any) {
      console.error("Registration error:", error);
      setError(
        error.response?.data?.message ||
          "An error occurred during registration. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col">
      <main className="flex max-md:flex-col container-fluid md:h-[calc(100vh-81px)]">
        {/* Left side - Image */}
        <div className="md:w-1/2 max-md:h-[450px] relative">
          <Image
            src="/auth/signup.svg"
            alt="Mountain landscape"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - Sign Up form */}
        <div className="w-full md:w-1/2 flex items-center max-md:justify-center justify-end">
          <div className="w-full max-w-[560px] lg:pr-20 max-xl:px-5">
            <div className="py-10">
              <h1 className="text-display-md text-paragraphContent max-md:text-center">
                Provider Sign Up
              </h1>
            </div>

            <form onSubmit={handleSubmit} className="space-y-5">
              {error && (
                <div className="text-red-500 text-sm bg-red-50 p-2 border border-red-200">
                  {error}
                </div>
              )}

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="fullName"
                    className="text-body-medium text-paragraphContent"
                  >
                    Full Name
                  </label>
                  <Input
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleChange}
                    className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    We won't show your full name publicly
                  </p>
                </div>

                <div>
                  <label
                    htmlFor="username"
                    className="text-body-medium text-paragraphContent"
                  >
                    Username
                  </label>
                  <div className="flex items-center bg-landingBackground border-0">
                    <span className="text-gray-500 pl-3">@</span>
                    <Input
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground"
                      required
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    This will be public. You can change it anytime.
                  </p>
                </div>
              </div>

              {/* Licensed State and Affiliated Organization */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="licensedState"
                    className="text-body-medium text-paragraphContent mb-1"
                  >
                    Licensed State
                  </label>
                  <div className="relative">
                    <select
                      id="licensedState"
                      name="licensedState"
                      value={formData.licensedState}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray appearance-none text-gray-500"
                      required
                    >
                      <option value="">Select a State</option>
                      <option value="AL">Alabama</option>
                      <option value="AK">Alaska</option>
                      {/* Add more states as needed */}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <ChevronDown className="text-gray-500" />
                    </div>
                  </div>
                </div>
                <div>
                  <label
                    htmlFor="affiliatedOrganization"
                    className="text-body-medium text-paragraphContent mb-1"
                  >
                    Affiliated Organization
                  </label>
                  <Input
                    type="text"
                    id="affiliatedOrganization"
                    name="affiliatedOrganization"
                    value={formData.affiliatedOrganization}
                    onChange={handleChange}
                    className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray"
                    required
                  />
                </div>
              </div>

              {/* Email and Phone */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="email"
                    className="text-body-medium text-paragraphContent mb-1"
                  >
                    Email Address
                  </label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray"
                    required
                  />
                </div>
                <div>
                  <label
                    htmlFor="phoneNumber"
                    className="text-body-medium text-paragraphContent mb-1"
                  >
                    Phone Number
                  </label>
                  <Input
                    type="tel"
                    id="phoneNumber"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleChange}
                    className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray"
                    required
                  />
                </div>
              </div>

              {/* Password */}
              <div>
                <label
                  htmlFor="password"
                  className="text-body-medium text-paragraphContent mb-1"
                >
                  Password
                </label>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray pr-10"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-paragraphContent" />
                    ) : (
                      <Eye className="h-5 w-5 text-paragraphContent" />
                    )}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Password must be at least 6 characters long
                </p>
              </div>

              {/* Checkboxes */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="rememberMe"
                    name="rememberMe"
                    checked={formData.rememberMe}
                    onCheckedChange={(checked) =>
                      setFormData({ ...formData, rememberMe: checked === true })
                    }
                    className="border-paragraphContent"
                  />
                  <label
                    htmlFor="rememberMe"
                    className="text-body-medium text-paragraphContent"
                  >
                    Remember me on this device
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="signUpForUpdates"
                    checked={formData.signUpForUpdates}
                    onCheckedChange={(checked) =>
                      setFormData({
                        ...formData,
                        signUpForUpdates: checked === true,
                      })
                    }
                    className="border-paragraphContent"
                  />
                  <label
                    htmlFor="signUpForUpdates"
                    className="text-body-medium text-paragraphContent"
                  >
                    Sign me up for important news and updates
                  </label>
                </div>
              </div>

              {/* Sign Up Button */}
              <Button
                type="submit"
                variant="netural"
                className="w-full font-normal h-11"
                disabled={isLoading}
              >
                {isLoading ? "Registering..." : "Sign Up"}
              </Button>
            </form>

            {/* Login link */}
            <div className="text-sm mt-8 pt-8 border-t border-gray-200 mb-5">
              Already have an account?{" "}
              <Link href="/login" className="text-black font-medium underline">
                Log In
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
