"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Eye, EyeOff, Calendar, ChevronDown } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useDispatch } from 'react-redux';
import { setUser } from '@/store/userSlice';
import { authService } from '@/services/api';

export default function IndividualSignupPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{
    fullName?: string;
    username?: string;
    dateOfBirth?: string;
    email?: string;
    phone?: string;
    password?: string;
  }>({});
  const [formData, setFormData] = useState({
    fullName: "",
    username: "",
    dateOfBirth: "",
    privacyCategory: "",
    email: "",
    phone: "",
    password: "",
    rememberMe: false,
    signUpForUpdates: true
  });
  const dispatch = useDispatch();

  const validateForm = () => {
    const errors: typeof validationErrors = {};
    
    // Full Name validation
    if (!formData.fullName.trim()) {
      errors.fullName = "Full name is required";
    } else if (formData.fullName.length < 2) {
      errors.fullName = "Full name must be at least 2 characters";
    }

    // Username validation
    if (!formData.username.trim()) {
      errors.username = "Username is required";
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      errors.username = "Username can only contain letters, numbers, and underscores";
    }

    // Date of Birth validation
    if (!formData.dateOfBirth) {
      errors.dateOfBirth = "Date of birth is required";
    } else {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 13) {
        errors.dateOfBirth = "You must be at least 13 years old";
      }
    }

    // Email validation
    if (!formData.email) {
      errors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
    }

    // Phone validation
    if (!formData.phone) {
      errors.phone = "Phone number is required";
    } else if (!/^\+?[\d\s-]{10,}$/.test(formData.phone)) {
      errors.phone = "Please enter a valid phone number";
    }

    // Password validation
    if (!formData.password) {
      errors.password = "Password is required";
    } else if (formData.password.length < 8) {
      errors.password = "Password must be at least 8 characters";
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(formData.password)) {
      errors.password = "Password must contain uppercase, lowercase, number, and special character";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    setValidationErrors({});

    if (!validateForm()) {
      setIsSubmitting(false);
      return;
    }

    try {
      // Ensure username starts with @
      const username = formData.username.startsWith('@') ? formData.username : `@${formData.username}`;

      const response = await authService.registerIndividual({
        fullName: formData.fullName,
        username,
        email: formData.email,
        password: formData.password,
        phoneNumber: formData.phone,
        birthday: formData.dateOfBirth,
        role: 4 // Patient role (individual users are patients)
      });

      if (!response.success) {
        // Handle validation errors from backend
        if (response.errors) {
          const formattedErrors: typeof validationErrors = {};
          
          // Map backend error fields to frontend validation errors
          Object.keys(response.errors).forEach(key => {
            switch(key) {
              case 'fullName':
                formattedErrors.fullName = response.errors[key];
                break;
              case 'username':
                formattedErrors.username = response.errors[key];
                break;
              case 'birthday':
                formattedErrors.dateOfBirth = response.errors[key];
                break;
              case 'email':
                formattedErrors.email = response.errors[key];
                break;
              case 'phoneNumber':
                formattedErrors.phone = response.errors[key];
                break;
              case 'password':
                formattedErrors.password = response.errors[key];
                break;
              case 'general':
                setError(response.errors[key]);
                break;
              default:
                setError(response.errors[key]);
            }
          });
          
          setValidationErrors(formattedErrors);
        }
        return;
      }

      // Store user data in Redux
      dispatch(setUser(response.data.user));

      // Redirect to individual dashboard
      router.push('/dashboard');
    } catch (error: any) {
      console.error("Signup error:", error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value
    });
    // Clear validation error when user starts typing
    if (validationErrors[name as keyof typeof validationErrors]) {
      setValidationErrors({
        ...validationErrors,
        [name]: undefined
      });
    }
  };

  return (
    <div className="flex flex-col">
      <main className="flex max-md:flex-col container-fluid lg:h-[calc(100vh-80px)]">
        {/* Left side - Image */}
        <div className="max-md:h-[450px] md:w-1/2 relative">
          <Image
            src="/auth/individual.svg"
            alt="Woman with headphones writing notes"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - Individual Sign Up Form */}
        <div className="w-full md:w-1/2 flex items-center justify-center">
          <div className="w-full max-w-[670px] px-8 py-12">
            <div>
              <h1 className="text-display-md text-paragraphContent mb-1 max-lg:text-3xl max-md:text-center">Create an Individual Account</h1>
              <p className="text-body-lg mb-6 text-paragraphContent max-md:text-center">
                Rhoncus morbi et augue nec, in id ullamcorper et sit.
              </p>

              <form onSubmit={onSubmit} className="space-y-4 mt-14">

                {/* Full Name and Username */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="fullName" className="text-body-medium text-paragraphContent">Full Name</label>
                    <Input
                      id="fullName"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleChange}
                      className={`w-full p-3 bg-landingBackground border-0 border-b-2 ${validationErrors.fullName ? 'border-red-500' : 'border-medium-gray'}`}
                      disabled={isSubmitting}
                      required
                    />
                    {validationErrors.fullName && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.fullName}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">We won't show your full name publicly</p>
                  </div>

                  <div>
                    <label htmlFor="username" className="text-body-medium text-paragraphContent">Username</label>
                    <div className={`flex items-center bg-landingBackground border-0 ${validationErrors.username ? 'border-b-2 border-red-500' : ''}`}>
                      <span className="text-gray-500 pl-3">@</span>
                      <Input
                        id="username"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        className="w-full p-3 bg-landingBackground"
                        disabled={isSubmitting}
                        required
                      />
                    </div>
                    {validationErrors.username && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.username}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">This will be public. You can change it anytime.</p>
                  </div>
                </div>

                {/* Date of Birth and Privacy Category */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="dateOfBirth" className="text-body-medium text-paragraphContent">Date of Birth</label>
                    <div className="relative w-full">
                      <Input
                        id="dateOfBirthInput"
                        name="dateOfBirth"
                        type="text"
                        value={formData.dateOfBirth}
                        onChange={handleChange}
                        className={`w-full p-3 bg-landingBackground border-0 border-b-2 ${validationErrors.dateOfBirth ? 'border-red-500' : 'border-medium-gray'} pr-10 date-input`}
                        placeholder="mm/dd/yyyy"
                        disabled={isSubmitting}
                        required
                      />
                      {validationErrors.dateOfBirth && (
                        <p className="text-red-500 text-xs mt-1">{validationErrors.dateOfBirth}</p>
                      )}
                      <div
                        className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                        onClick={() => {
                          const dateInput = document.getElementById('datePickerHidden') as HTMLInputElement;
                          if (dateInput && typeof dateInput.showPicker === 'function') {
                            dateInput.showPicker();
                          }
                        }}
                      >
                        <div className="flex items-center justify-center w-6 h-6">
                          <Calendar className="h-5 w-5 text-gray-700" />
                        </div>
                      </div>
                      <Input
                        id="datePickerHidden"
                        type="date"
                        className="absolute opacity-0 w-0 h-0"
                        onChange={(e) => {
                          if (e.target.value) {
                            // Format the date as mm/dd/yyyy
                            const date = new Date(e.target.value);
                            const month = (date.getMonth() + 1).toString().padStart(2, '0');
                            const day = date.getDate().toString().padStart(2, '0');
                            const year = date.getFullYear();
                            const formattedDate = `${month}/${day}/${year}`;

                            // Update the visible input
                            const visibleInput = document.getElementById('dateOfBirthInput') as HTMLInputElement;
                            if (visibleInput) {
                              visibleInput.value = formattedDate;
                            }

                            // Update the form data
                            setFormData({
                              ...formData,
                              dateOfBirth: formattedDate
                            });
                          }
                        }}
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="privacyCategory" className="text-body-medium text-paragraphContent">Are you a Military veteran?</label>
                    <div className="relative text-gray-600">
                      <select
                        id="privacyCategory"
                        name="privacyCategory"
                        value={formData.privacyCategory}
                        onChange={handleChange}
                        className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray appearance-none"
                        disabled={isSubmitting}
                        required
                      >
                        <option value="">Select an option</option>
                        <option value="yes">Yes</option>
                        <option value="no">No</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <ChevronDown className="h-5 w-5" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Email and Phone */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="email" className="text-body-medium text-paragraphContent">Email Address</label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      className={`w-full p-3 bg-landingBackground border-0 border-b-2 ${validationErrors.email ? 'border-red-500' : 'border-medium-gray'}`}
                      disabled={isSubmitting}
                      required
                    />
                    {validationErrors.email && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.email}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="phone" className="text-body-medium text-paragraphContent">Phone Number</label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleChange}
                      className={`w-full p-3 bg-landingBackground border-0 border-b-2 ${validationErrors.phone ? 'border-red-500' : 'border-medium-gray'}`}
                      disabled={isSubmitting}
                      required
                    />
                    {validationErrors.phone && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.phone}</p>
                    )}
                  </div>
                </div>

                {/* Password */}
                <div>
                  <label htmlFor="password" className="text-body-medium text-paragraphContent">Create a Password</label>
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={handleChange}
                      className={`w-full p-3 bg-landingBackground border-0 border-b-2 ${validationErrors.password ? 'border-red-500' : 'border-medium-gray'} pr-10`}
                      disabled={isSubmitting}
                      required
                    />
                    {validationErrors.password && (
                      <p className="text-red-500 text-xs mt-1">{validationErrors.password}</p>
                    )}
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-paragraphContent" />
                      ) : (
                        <Eye className="h-5 w-5 text-paragraphContent" />
                      )}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Your password must be a combination of numbers, letters, symbols, and capitals.</p>
                </div>

                {/* Checkboxes */}
                <div className="space-y-3 mt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="rememberMe"
                      name="rememberMe"
                      checked={formData.rememberMe}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, rememberMe: checked === true })
                      }
                      disabled={isSubmitting}
                      className="border-paragraphContent"
                    />
                    <label
                      htmlFor="rememberMe"
                      className="text-paragraphContent text-body-medium"
                    >
                      Remember me on this device
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="signUpForUpdates"
                      name="signUpForUpdates"
                      checked={formData.signUpForUpdates}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, signUpForUpdates: checked === true })
                      }
                      disabled={isSubmitting}
                      className="border-paragraphContent"
                    />
                    <label
                      htmlFor="signUpForUpdates"
                      className="text-paragraphContent text-body-medium"
                    >
                      Sign me up for important news and updates
                    </label>
                  </div>
                </div>

                {/* Sign Up Button */}
                <Button
                  type="submit"
                  variant="netural"
                  className="w-full font-normal h-11 rounded-sm"
                  disabled={isSubmitting}
                >
                  <span className="text-white text-title-medium">{isSubmitting ? "Signing up..." : "Sign Up"}</span>
                </Button>
              </form>

              {/* Login link */}
              <div className="text-sm mt-8 pt-8 border-t border-gray-200">
                Already have an account? <Link href="/login" className="font-medium underline">Log In</Link>
              </div>

              {/* Provider link */}
              <div className="text-sm mt-4">
                Are you a healthcare provider? <Link href="/signup/provider" className="font-medium underline">Create a Provider Account</Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
