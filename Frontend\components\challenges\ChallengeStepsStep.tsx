"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { GripVertical } from "lucide-react";
import { PreviewNewProgram } from "@/utils/icons";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { UploadIcon } from "@/utils/icons";

const DIFFICULTY = ["Easy", "Challenging", "Hard"];
const DURATION = ["5 mins", "10 mins", "15 mins", "30 mins", "1 hour"];
const REWARD = ["10 pts", "20 pts", "30 pts", "50 pts"];
const QUESTION_TYPES = ["Valence Scale", "True/False"];

const ALLOWED_IMAGE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
];
const ALLOWED_VIDEO_TYPES = [
  "video/mp4",
  "video/webm",
  "video/ogg",
  "video/quicktime",
  "video/mov",
];
const ALLOWED_TYPES = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_VIDEO_TYPES];

interface Step {
  id?: string;
  name: string;
  difficulty: string;
  duration: string;
  reward: string;
  media: string;
  addMedia: boolean;
  addQuestionnaire: boolean;
  questions: Array<{ text: string; type: string }>;
}

interface SortableStepProps {
  id: string;
  index: number;
  step: Step;
  onEdit: (index: number) => void;
  onRemove: (index: number) => void;
  isEditing: boolean;
}

function SortableStep({
  id,
  index,
  step,
  onEdit,
  onRemove,
  isEditing,
}: SortableStepProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <Card className="px-4 py-2 h-[54px] border rounded mb-3 cursor-move flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div {...attributes} {...listeners} className="cursor-grab">
            <GripVertical className="w-5 h-5 text-gray-400" />
          </div>
          <span className="font-bold text-title-medium text-paragraphContent">
            {step.name}
          </span>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outlined"
            size="sm"
            className="text-primary h-8"
            onClick={() => onEdit(index)}
          >
            <span className="text-body-medium">
              {isEditing ? "Cancel" : "Edit"}
            </span>
          </Button>
          <Button
            variant="outlined"
            size="sm"
            className="text-body-medium text-primary h-8"
            onClick={() => onRemove(index)}
          >
            <span className="text-body-medium">Remove</span>
          </Button>
        </div>
      </Card>
    </div>
  );
}

export default function ChallengeStepsStep({
  lockOrder,
  setLockOrder,
  stepForm,
  setStepForm,
  steps,
  setSteps,
  handleStepMediaChange,
  handleRemoveStepMedia,
  handleAddStep,
  handleRemoveStep,
  handleAddQuestion,
  VALENCE_ICONS,
  TRUE_FALSE_ICONS,
  onBack,
  onNext,
}: any) {
  const [validationErrors, setValidationErrors] = useState<{
    name?: string;
    difficulty?: string;
    duration?: string;
    reward?: string;
    media?: string;
    questions?: string;
    steps?: string;
    questionText?: string;
    questionType?: string;
    questionValence?: string;
    questionTrueFalse?: string;
  }>({});
  const [editingStepIndex, setEditingStepIndex] = useState<number | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleNext = () => {
    // Check if there's at least one step
    if (steps.length === 0) {
      setValidationErrors({ steps: "Please add at least one step" });
      return;
    }

    // Validate that each step with a questionnaire has at least one question
    const hasInvalidStep = steps.some((step: Step) => {
      return step.addQuestionnaire && (!step.questions || step.questions.length === 0);
    });

    if (hasInvalidStep) {
      setValidationErrors({ steps: "Each step with a questionnaire must have at least one question." });
      return;
    }

    // Proceed to next step without validating details
    onNext();
  };

  const validateStepForm = () => {
    const errors: {
      name?: string;
      difficulty?: string;
      duration?: string;
      reward?: string;
      media?: string;
      questions?: string;
    } = {};

    if (!stepForm.name.trim()) {
      errors.name = "Step name is required";
    }

    if (!stepForm.difficulty) {
      errors.difficulty = "Please select difficulty";
    }

    if (!stepForm.duration) {
      errors.duration = "Please select duration";
    }

    if (!stepForm.reward) {
      errors.reward = "Please select reward";
    }

    if (stepForm.addMedia && !stepForm.media) {
      errors.media = "Please upload media for this step";
    }

    // Enforce at least one question if questionnaire is enabled
    if (stepForm.addQuestionnaire) {
      if (!stepForm.questions || stepForm.questions.length === 0) {
        errors.questions = "Each step with a questionnaire must have at least one question.";
      } else if (!validateQuestions()) {
        errors.questions = "Please complete all questions";
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleAddStepWithValidation = () => {
    if (stepForm.addQuestionnaire && (!stepForm.questions || stepForm.questions.length === 0)) {
      setValidationErrors({ questions: "Each step with a questionnaire must have at least one question." });
      return;
    }
    if (validateStepForm()) {
      handleAddStep();
    }
  };

  const validateQuestion = (question: any) => {
    const errors: {
      text?: string;
      type?: string;
    } = {};

    if (!question.text.trim()) {
      errors.text = "Question text is required";
    }

    if (!question.type) {
      errors.type = "Question type is required";
    }

    return errors;
  };

  const handleAddQuestionWithValidation = () => {
    // Add the question first
    handleAddQuestion();

    // Clear any existing question validation errors
    setValidationErrors((prev) => ({
      ...prev,
      questionText: undefined,
      questionType: undefined,
      questions: undefined,
    }));
  };

  const validateQuestions = () => {
    if (!stepForm.questions.length) {
      return false;
    }

    // Validate all questions
    const hasErrors = stepForm.questions.some((q: any) => {
      const errors = validateQuestion(q);
      return Object.keys(errors).length > 0;
    });

    if (hasErrors) {
      setValidationErrors((prev) => ({
        ...prev,
        questions: "Please complete all questions",
      }));
      return false;
    }

    return true;
  };

  const handleQuestionChange = (idx: number, field: string, value: string) => {
    setStepForm((f: any) => ({
      ...f,
      questions: f.questions.map((q: any, i: number) =>
        i === idx ? { ...q, [field]: value } : q
      ),
    }));

    // Clear validation errors when user starts typing
    if (field === "text") {
      setValidationErrors((prev) => ({
        ...prev,
        questionText: undefined,
        questions: undefined,
      }));
    } else if (field === "type") {
      setValidationErrors((prev) => ({
        ...prev,
        questionType: undefined,
        questions: undefined,
      }));
    }
  };

  const handleEditStep = (index: number) => {
    if (editingStepIndex === index) {
      // If clicking edit on the currently edited step, cancel editing
      setEditingStepIndex(null);
      setStepForm({
        name: "",
        difficulty: DIFFICULTY[0],
        duration: DURATION[0],
        reward: REWARD[0],
        media: "",
        addMedia: false,
        addQuestionnaire: false,
        questions: [],
      });
    } else {
      // Start editing the selected step
      setEditingStepIndex(index);
      setStepForm({ ...steps[index] });
    }
  };

  const handleUpdateStep = () => {
    if (validateStepForm() && editingStepIndex !== null) {
      setSteps((prev: Step[]) =>
        prev.map((step: Step, idx: number) =>
          idx === editingStepIndex ? { ...stepForm } : step
        )
      );
      setEditingStepIndex(null);
      setStepForm({
        name: "",
        difficulty: DIFFICULTY[0],
        duration: DURATION[0],
        reward: REWARD[0],
        media: "",
        addMedia: false,
        addQuestionnaire: false,
        questions: [],
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingStepIndex(null);
    setStepForm({
      name: "",
      difficulty: DIFFICULTY[0],
      duration: DURATION[0],
      reward: REWARD[0],
      media: "",
      addMedia: false,
      addQuestionnaire: false,
      questions: [],
    });
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setSteps((prev: Step[]) => {
        const oldIndex = prev.findIndex(
          (step) => `step-${step.id || prev.indexOf(step)}` === active.id
        );
        const newIndex = prev.findIndex(
          (step) => `step-${step.id || prev.indexOf(step)}` === over.id
        );

        // Update the steps array with the new order
        const newSteps = arrayMove(prev, oldIndex, newIndex);

        // If we're editing a step, update the editingStepIndex
        if (editingStepIndex !== null) {
          if (editingStepIndex === oldIndex) {
            setEditingStepIndex(newIndex);
          } else if (
            (editingStepIndex > oldIndex && editingStepIndex <= newIndex) ||
            (editingStepIndex < oldIndex && editingStepIndex >= newIndex)
          ) {
            setEditingStepIndex(
              editingStepIndex + (oldIndex < newIndex ? -1 : 1)
            );
          }
        }

        return newSteps;
      });
    }
  };

  const renderStepForm = (isEditing: boolean = false) => (
    <div>
      <div className="p-4 border rounded mt-4">
        <div className="relative mb-2">
          <Input
            className={`mb-1 bg-landingBackground ${
              validationErrors.name ? "border-red-500" : ""
            }`}
            placeholder={isEditing ? "Edit step name..." : "Name the step..."}
            value={stepForm.name}
            onChange={(e) =>
              setStepForm((f: any) => ({ ...f, name: e.target.value }))
            }
          />
          {validationErrors.name && (
            <p className="text-red-500 text-sm">{validationErrors.name}</p>
          )}
        </div>
        <div className="flex gap-4 mb-8">
          <div className="flex-1">
            <Select
              value={stepForm.difficulty}
              onValueChange={(val: string) =>
                setStepForm((f: any) => ({ ...f, difficulty: val }))
              }
            >
              <SelectTrigger
                className={`bg-landingBackground ${
                  validationErrors.difficulty ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="Difficulty..." />
              </SelectTrigger>
              <SelectContent className="bg-landingBackground">
                {DIFFICULTY.map((d: string) => (
                  <SelectItem key={d} value={d}>
                    {d}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.difficulty && (
              <p className="text-red-500 text-sm mt-1">
                {validationErrors.difficulty}
              </p>
            )}
          </div>
          <div className="flex-1">
            <Select
              value={stepForm.duration}
              onValueChange={(val: string) =>
                setStepForm((f: any) => ({ ...f, duration: val }))
              }
            >
              <SelectTrigger
                className={`bg-landingBackground ${
                  validationErrors.duration ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="Duration..." />
              </SelectTrigger>
              <SelectContent className="bg-landingBackground">
                {DURATION.map((d: string) => (
                  <SelectItem key={d} value={d}>
                    {d}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.duration && (
              <p className="text-red-500 text-sm mt-1">
                {validationErrors.duration}
              </p>
            )}
          </div>
          <div className="flex-1">
            <Select
              value={stepForm.reward}
              onValueChange={(val: string) =>
                setStepForm((f: any) => ({ ...f, reward: val }))
              }
            >
              <SelectTrigger
                className={`bg-landingBackground ${
                  validationErrors.reward ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="Reward..." />
              </SelectTrigger>
              <SelectContent className="bg-landingBackground">
                {REWARD.map((r: string) => (
                  <SelectItem key={r} value={r}>
                    {r}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.reward && (
              <p className="text-red-500 text-sm mt-1">
                {validationErrors.reward}
              </p>
            )}
          </div>
        </div>
        <div className="flex items-center my-3">
          <Switch
            checked={stepForm.addMedia}
            onCheckedChange={(v: boolean) =>
              setStepForm((f: any) => ({ ...f, addMedia: v }))
            }
          />
          <span className="ml-2 text-body-medium text-paragraphContent">
            Add a video, document, or image to this step
          </span>
        </div>
        {stepForm.addMedia && (
          <div>
            <div className="flex flex-col md:flex-row justify-between w-full gap-4">
              <div className="flex w-full md:w-auto">
                <div className="flex justify-center items-center mb-2 md:mb-0 md:mr-4 w-full md:w-[88px] h-[120px] md:h-[65px] overflow-hidden">
                  {stepForm.media ? (
                    (() => {
                      const type = (() => {
                        if (
                          stepForm.media.startsWith("data:video") ||
                          stepForm.media.match(
                            /\.(mp4|webm|ogg|mov|quicktime)$/i
                          )
                        )
                          return "video";
                        if (
                          stepForm.media.startsWith("data:image") ||
                          stepForm.media.match(/\.(jpg|jpeg|png|gif|webp)$/i)
                        )
                          return "image";
                        if (
                          stepForm.media.startsWith("data:application/pdf") ||
                          stepForm.media.match(/\.(pdf)$/i)
                        )
                          return "pdf";
                        return "other";
                      })();
                      if (type === "image") {
                        return (
                          <Image
                            src={stepForm.media}
                            alt="Preview"
                            width={88}
                            height={65}
                            className="rounded object-cover w-full h-full"
                          />
                        );
                      }
                      if (type === "video") {
                        return (
                          <video
                            src={stepForm.media}
                            controls
                            width={88}
                            height={65}
                            className="rounded object-cover w-full h-full"
                          />
                        );
                      }
                      if (type === "pdf") {
                        return (
                          <iframe
                            src={stepForm.media}
                            width={88}
                            height={65}
                            className="rounded object-cover w-full h-full"
                            title="Document preview"
                          />
                        );
                      }
                      return (
                        <a
                          href={stepForm.media}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 underline"
                        >
                          Open Document
                        </a>
                      );
                    })()
                  ) : (
                    <PreviewNewProgram />
                  )}
                </div>
                <div className="flex flex-col justify-center w-full md:w-auto">
                  <input
                    type="file"
                    accept="image/*,video/*"
                    className="hidden"
                    id="file-upload"
                    onChange={handleStepMediaChange}
                  />
                  <div className="flex flex-col justify-center items-center">
                    <label
                      htmlFor="file-upload"
                      className="flex items-center gap-2 border border-darkBlueNormal rounded px-4 py-1 h-8 text-darkBlueNormal text-body-medium bg-white cursor-pointer hover:bg-landingBackground"
                    >
                      <UploadIcon />
                      Upload
                    </label>
                    <a
                      className="text-body-medium text-paragraphContent mt-2 cursor-pointer"
                      onClick={handleRemoveStepMedia}
                    >
                      remove
                    </a>
                  </div>
                </div>
              </div>
              <div className="hidden md:block w-px bg-darkBlueBackground h-18" />
              <div className="block md:hidden border-t border-gray-200" />
              <div className="flex flex-col md:justify-around gap-2 w-full mt-4 md:mt-0">
                <div className="text-paragraphContent flex flex-col justify-center text-body-medium ">
                  <div className="mb-2">Image requirements:</div>
                  <div className="ml-1">1. Min. 400 x 400px</div>
                  <div className="ml-1">2. Max. 2MB</div>
                </div>
              </div>
            </div>
            {validationErrors.media && (
              <p className="text-red-500 text-center text-sm mt-1">
                {validationErrors.media}
              </p>
            )}
          </div>
        )}
        <div className="flex items-center my-6">
          <Switch
            checked={stepForm.addQuestionnaire}
            onCheckedChange={(v: boolean) => {
              setStepForm((f: any) => ({
                ...f,
                addQuestionnaire: v,
                // Add a default question when enabling questionnaire
                questions: v ? [{ text: "", type: QUESTION_TYPES[0] }] : [],
              }));
            }}
          />
          <span className="ml-2 text-body-medium text-paragraphContent">
            Add a questionnaire to this step
          </span>
        </div>
        {stepForm.addQuestionnaire && (
          <div>
            {stepForm.questions.map((q: any, idx: number) => (
              <div key={idx} className="mb-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center mb-2 gap-2">
                  <span className="font-bold min-w-28 text-paragraphContent text-body-medium">
                    Question #{idx + 1}:
                  </span>
                  <div className="flex-1 w-full">
                    <Input
                      className={`w-full bg-landingBackground ${
                        validationErrors.questionText &&
                        idx === stepForm.questions.length - 1
                          ? "border-red-500"
                          : ""
                      }`}
                      placeholder="Enter question..."
                      value={q.text}
                      onChange={(e) =>
                        handleQuestionChange(idx, "text", e.target.value)
                      }
                    />
                    {validationErrors.questionText &&
                      idx === stepForm.questions.length - 1 && (
                        <p className="text-red-500 text-sm mt-1">
                          {validationErrors.questionText}
                        </p>
                      )}
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                  <Label className="font-bold min-w-28  max-md:mt-4 text-paragraphContent text-body-medium">
                    Response:
                  </Label>
                  <div className="flex-1 w-full">
                    <Select
                      value={q.type}
                      onValueChange={(val: string) =>
                        handleQuestionChange(idx, "type", val)
                      }
                    >
                      <SelectTrigger
                        className={`w-full w-[180px] max-md:w-full bg-landingBackground ${
                          validationErrors.questionType &&
                          idx === stepForm.questions.length - 1
                            ? "border-red-500"
                            : ""
                        }`}
                      >
                        <SelectValue placeholder="Select type..." />
                      </SelectTrigger>
                      <SelectContent className="bg-landingBackground">
                        {QUESTION_TYPES.map((t: string) => (
                          <SelectItem key={t} value={t}>
                            {t}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {validationErrors.questionType &&
                      idx === stepForm.questions.length - 1 && (
                        <p className="text-red-500 text-sm mt-1">
                          {validationErrors.questionType}
                        </p>
                      )}
                  </div>
                  <div className="w-full mt-2 sm:mt-0">
                    {q.type === "Valence Scale" && (
                      <div className="flex gap-1 justify-center">
                        {VALENCE_ICONS.map((icon: string) => (
                          <span key={icon}>{icon}</span>
                        ))}
                      </div>
                    )}
                    {q.type === "True/False" && (
                      <div className="flex gap-6 justify-center">
                        {TRUE_FALSE_ICONS.map((icon: string) => (
                          <span key={icon}>{icon}</span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            <Button
              variant="outlinedDark"
              size="sm"
              className="w-full h-8"
              onClick={handleAddQuestionWithValidation}
            >
              + Add a Question
            </Button>
            {validationErrors.questions && (
              <p className="text-red-500 text-sm mt-2 text-center">
                {validationErrors.questions}
              </p>
            )}
          </div>
        )}
      </div>
      <div className="flex gap-2 mt-2">
        {isEditing ? (
          <>
            <Button
              variant="outlined"
              size="sm"
              className="flex-1 h-8 text-primary"
              onClick={handleUpdateStep}
            >
              <span className="text-body-medium">Update Step</span>
            </Button>
            <Button
              variant="outlined"
              size="sm"
              className="flex-1 h-8 text-primary"
              onClick={handleCancelEdit}
            >
              <span className="text-body-medium">Cancel</span>
            </Button>
          </>
        ) : (
          <Button
            variant="outlinedDark"
            size="sm"
            className="w-full h-8"
            onClick={handleAddStepWithValidation}
          >
            + Add a Step
          </Button>
        )}
      </div>
    </div>
  );

  return (
    <div className="flex justify-center w-full">
      <div className="px-2 py-4 sm:p-8 w-full max-w-full sm:max-w-[550px] mt-4 sm:mt-8">
        <div className="text-body-lg font-bold mb-6">Add Challenge Steps:</div>
        <div className="flex items-center justify-between mb-4 border-b border-gray-200 pb-4">
          <div className="flex items-center">
            <Switch checked={lockOrder} onCheckedChange={setLockOrder} />
            <span className="ml-2 text-title-medium">
              Lock the order of the steps
            </span>
          </div>
        </div>
        {!lockOrder && (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={steps.map(
                (step: Step, index: number) => `step-${step.id || index}`
              )}
              strategy={verticalListSortingStrategy}
            >
              <div className="flex flex-col gap-2 w-full">
                {steps.map((step: Step, index: number) => (
                  <div key={`step-${step.id || index}`} className="w-full">
                    <SortableStep
                      id={`step-${step.id || index}`}
                      index={index}
                      step={step}
                      onEdit={handleEditStep}
                      onRemove={handleRemoveStep}
                      isEditing={editingStepIndex === index}
                    />
                    {editingStepIndex === index && renderStepForm(true)}
                  </div>
                ))}
              </div>
            </SortableContext>
          </DndContext>
        )}
        {lockOrder && (
          <>
            {steps.map((s: Step, idx: number) => (
              <div key={idx} className="w-full">
                <Card className="px-4 py-2 h-auto border rounded my-2 flex flex-col sm:flex-row justify-between items-center w-full">
                  <span className="font-bold text-title-medium text-paragraphContent max-w-full truncate text-base w-full sm:w-auto">
                    {idx + 1}. {s.name}
                  </span>
                  <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto mt-2 sm:mt-0">
                    <Button
                      variant="outlined"
                      size="sm"
                      className="text-primary h-8 w-full sm:w-auto"
                      onClick={() => handleEditStep(idx)}
                    >
                      <span className="text-body-medium">
                        {editingStepIndex === idx ? "Cancel" : "Edit"}
                      </span>
                    </Button>
                    {/* <Button
                      variant="outlined"
                      size="sm"
                      className="text-body-medium text-primary h-8 w-full sm:w-auto"
                      onClick={() => handleRemoveStep(idx)}
                    >
                      <span className="text-body-medium">Remove</span>
                    </Button> */}
                  </div>
                </Card>
                {editingStepIndex === idx && renderStepForm(true)}
              </div>
            ))}
          </>
        )}
        {editingStepIndex === null && renderStepForm(false)}
        {validationErrors.steps && (
          <div className="text-red-500 text-sm text-center mt-4">
            {validationErrors.steps}
          </div>
        )}
        <div className="flex flex-col sm:flex-row justify-between gap-2 mt-8 w-full">
          <Button
            variant="netural"
            size="sm"
            className="text-lightBlue h-8 w-full sm:w-auto"
            onClick={onBack}
          >
            <span className="text-body-medium">Back</span>
          </Button>
          <Button
            variant="netural"
            size="sm"
            className="h-8 text-lightBlue w-full sm:w-auto"
            onClick={handleNext}
          >
            <span className="text-body-medium">Preview</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
