const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  fullName: {
    type: String,
    required: [true, 'Full name is required'],
    trim: true
  },
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    validate: {
      validator: function(v) {
        return v.startsWith('@');
      },
      message: 'Username must start with @'
    }
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: 6
  },
  phoneNumber: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true
  },
  role: {
    type: Number,
    enum: [0, 1, 2, 3, 4], // 0: super admin, 1: admin, 2: local admin, 3: provider, 4: patient
    default: 4
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  // Provider specific fields
  licensedState: {
    type: String,
    required: function() {
      return this.role === 3;
    }
  },
  affiliatedOrganization: {
    type: String,
    required: function() {
      return this.role === 3;
    }
  },
  // Patient specific fields
  birthday: {
    type: Date,
    required: function() {
      return this.role === 4;
    }
  },
  militaryVeteran: {
    type: Boolean,
    default: false,
    required: function() {
      return this.role === 4;
    }
  },
  avatar: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model('User', userSchema);

module.exports = User; 