"use client";
import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";

import { ProfilePhotoCard } from "@/components/account/ProfilePhotoCard";
import { ProfileDetailsCard } from "@/components/account/ProfileDetailsCard";
import { ProfileBillingCard } from "@/components/account/ProfileBillingCard";
import { ProfileNotificationCard } from "@/components/account/ProfieNotificationcard";

const initialProfile = {
  name: "<PERSON>",
  username: "@lizthetherapist",
  state: "Florida",
  org: "White Sands",
  email: "<EMAIL>",
  phone: "+****************",
  photo: "",
};

const card = {
  name: "ADRIAN TRA",
  number: "4889 9271 1937 1932",
  expiry: "12/28",
  cvv: "***",
};

function AccountPageContent() {
  const searchParams = useSearchParams();
  const tabParam = searchParams.get("tab");

  // Set initial tab based on query param, fallback to "profile"
  const [tab, setTab] = useState<"profile" | "billing" | "notifications">(
    (tabParam === "notifications" || tabParam === "billing" || tabParam === "profile")
      ? (tabParam as "profile" | "billing" | "notifications")
      : "profile"
  );

  // If the user navigates to a new tab param, update the tab state
  useEffect(() => {
    if (tabParam && (tabParam === "notifications" || tabParam === "billing" || tabParam === "profile")) {
      setTab(tabParam as "profile" | "billing" | "notifications");
    }
  }, [tabParam]);

  const [profile, setProfile] = useState(initialProfile);
  const [editProfile, setEditProfile] = useState(initialProfile);
  const [photoPreview, setPhotoPreview] = useState<string>(profile.photo);
  const [isDirty, setIsDirty] = useState(false);

  // Handle photo upload
  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPhotoPreview(reader.result as string);
        setEditProfile((prev) => ({ ...prev, photo: reader.result as string }));
        setIsDirty(true);
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove photo
  const handleRemovePhoto = () => {
    setPhotoPreview("");
    setEditProfile((prev) => ({ ...prev, photo: "" }));
    setIsDirty(true);
  };

  // Handle profile field change
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setEditProfile((prev) => ({ ...prev, [name]: value }));
    setIsDirty(true);
  };

  // Save profile
  const handleSave = () => {
    setProfile(editProfile);
    setIsDirty(false);
  };

  return (
    <div className="flex flex-col">
      <p className="text-display-md text-paragraphContent mb-6 max-md:text-3xl">My Account</p>
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Tabs */}
        <div className="w-full lg:w-auto">
          <div className="bg-white rounded border p-0.5 w-[220px] max-md:w-full">
            <button
              className={`flex-1 block w-full text-left px-4 py-2 sm:py-3 rounded font-medium text-sm sm:text-base text-title-medium ${
                tab === "profile"
                  ? "bg-landingBackground text-[#21272A]"
                  : "hover:bg-gray-50 text-gray-700"
              }`}
              onClick={() => setTab("profile")}
            >
              Profile
            </button>
            <button
              className={`flex-1 block w-full text-left px-4 py-2 sm:py-3 rounded font-medium text-sm sm:text-base text-title-medium ${
                tab === "billing"
                  ? "bg-landingBackground text-[#21272A]"
                  : "hover:bg-gray-50 text-gray-700"
              }`}
              onClick={() => setTab("billing")}
            >
              Billing
            </button>
            <button
              className={`flex-1 block w-full text-left px-4 py-2 sm:py-3 rounded font-medium text-sm sm:text-base text-title-medium ${
                tab === "notifications"
                  ? "bg-landingBackground text-[#21272A]"
                  : "hover:bg-gray-50 text-gray-700"
              }`}
              onClick={() => setTab("notifications")}
            >
              Notifications
            </button>
          </div>
        </div>

        {/* Profile Tab */}
        {tab === "profile" && (
          <div className="rounded max-md:px-0 w-[597px] max-md:w-full">
            <div className="flex flex-col gap-6 ">
              <ProfilePhotoCard
                photoPreview={photoPreview}
                onPhotoChange={handlePhotoChange}
                onRemove={handleRemovePhoto}
              />
              <ProfileDetailsCard
                profile={editProfile}
                onChange={handleChange}
                onSave={handleSave}
                isDirty={isDirty}
              />
            </div>
          </div>
        )}

        {/* Billing Tab */}
        {tab === "billing" && <ProfileBillingCard card={card} />}

        {/* Notifications Tab */}
        {tab === "notifications" && <ProfileNotificationCard />}
      </div>
    </div>
  );
}

export default function AccountPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading...</div>
      </div>
    }>
      <AccountPageContent />
    </Suspense>
  );
}
