"use client";
import { usePathname } from 'next/navigation';
import { Header } from '@/components/header';

export default function ClientHeader() {
  const pathname = usePathname();
  const showHeader =
    pathname === '/' ||
    pathname.startsWith('/login') ||
    pathname.startsWith('/signup') ||
    pathname.startsWith('/forgot-password') ||
    pathname.startsWith('/verify-account');
  return showHeader ? <Header /> : null;
} 