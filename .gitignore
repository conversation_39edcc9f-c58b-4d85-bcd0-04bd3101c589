# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/Frontend/node_modules
/Frontend/.next
/Frontend/pnpm-lock.yaml
/Frontend/package-lock.json

/Backend/node_modules
/Backend/package-lock.json

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts