const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

// Generate JWT Token
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN
  });
};

class AuthController {
  // Register new user
  async register(req, res) {
    try {
      const {
        fullName,
        username,
        email,
        password,
        phoneNumber,
        role,
        // Provider specific fields
        licensedState,
        affiliatedOrganization,
        // Patient specific fields
        birthday,
        militaryVeteran
      } = req.body;

      // Check if user already exists
      const userExists = await User.findOne({ $or: [{ email }, { username }] });
      if (userExists) {
        return res.status(200).json({
          success: false,
          errors: {
            email: userExists.email === email ? 'Email already exists' : null,
            username: userExists.username === username ? 'Username already exists' : null
          }
        });
      }

      // Validate role-specific fields
      const validationErrors = {};
      if (role === 3) { // Provider
        if (!licensedState) validationErrors.licensedState = 'Licensed state is required for providers';
        if (!affiliatedOrganization) validationErrors.affiliatedOrganization = 'Affiliated organization is required for providers';
      } else if (role === 4) { // Patient
        if (!birthday) validationErrors.birthday = 'Birthday is required for patients';
      }

      if (Object.keys(validationErrors).length > 0) {
        return res.status(200).json({
          success: false,
          errors: validationErrors
        });
      }

      // Create user
      const user = await User.create({
        fullName,
        username,
        email,
        password,
        phoneNumber,
        role,
        licensedState,
        affiliatedOrganization,
        birthday,
        militaryVeteran,
        isVerified: false
      });

      // Generate token
      const token = generateToken(user._id);

      res.status(200).json({
        success: true,
        data: {
          user: {
            id: user._id,
            fullName: user.fullName,
            username: user.username,
            email: user.email,
            role: user.role,
            isVerified: user.isVerified
          },
          token
        }
      });
    } catch (error) {
      // Handle mongoose validation errors
      if (error.name === 'ValidationError') {
        const validationErrors = {};
        Object.keys(error.errors).forEach(key => {
          validationErrors[key] = error.errors[key].message;
        });
        return res.status(200).json({
          success: false,
          errors: validationErrors
        });
      }

      res.status(200).json({
        success: false,
        errors: {
          general: error.message || 'An error occurred during registration'
        }
      });
    }
  }

  // Login user
  async login(req, res) {
    try {
      const { email, password } = req.body;

      // Check if user exists
      const user = await User.findOne({ email });
      if (!user) {
        return res.status(401).json({
          status: 'error',
          message: 'Invalid credentials'
        });
      }

      // Check password
      const isMatch = await user.comparePassword(password);
      if (!isMatch) {
        return res.status(401).json({
          status: 'error',
          message: 'Invalid credentials'
        });
      }

      // Generate token
      const token = generateToken(user._id);

      res.status(200).json({
        status: 'success',
        data: {
          user: {
            id: user._id,
            fullName: user.fullName,
            username: user.username,
            email: user.email,
            role: user.role,
            isVerified: user.isVerified
          },
          token
        }
      });
    } catch (error) {
      res.status(400).json({
        status: 'error',
        message: error.message
      });
    }
  }

  // Get current user
  async getMe(req, res) {
    try {
      const user = await User.findById(req.user.id).select('-password');
      res.status(200).json({
        status: 'success',
        data: {
          user
        }
      });
    } catch (error) {
      res.status(400).json({
        status: 'error',
        message: error.message
      });
    }
  }
}

module.exports = new AuthController(); 