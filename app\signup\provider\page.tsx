"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Eye, EyeOff, ChevronDown } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useDispatch } from 'react-redux';
import { setUser } from '@/store/userSlice';

export default function ProviderSignupPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    fullName: "",
    username: "",
    licensedState: "",
    affiliatedOrganization: "",
    email: "",
    phone: "",
    password: "",
    rememberMe: false,
    signUpForUpdates: true
  });
  const dispatch = useDispatch();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    setIsLoading(true);
    e.preventDefault();
    // Handle form submission logic here
    // Dispatch user data and role to Redux
    dispatch(setUser({ role: 'provider', username: formData.username, email: formData.email }));
    router.push("/dashboard"); // Redirect to main dashboard route
  };

  return (
    <div className="flex flex-col">
      <main className="flex max-md:flex-col container-fluid lg:h-[calc(100vh-81px)]">
        {/* Left side - Image */}
        <div className="max-md:h-[450px] md:w-1/2 w-full relative">
          <Image
            src="/auth/provider.svg"
            alt="People sharing a meal"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - Provider Sign Up Form */}
        <div className="w-full md:w-1/2 flex items-center justify-end">
          <div className="w-full max-w-[650px] xl:pr-20 max-xl:px-5">
            <div>
              <div className="max-md:text-center">
                <h1 className="text-display-md mb-1 max-md:text-3xl max-md:mt-5">Create a Provider Account</h1>
                <p className="text-paragraphContent mb-6 text-body-lg">
                  Rhoncus morbi et augue nec, in id ullamcorper et sit.
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Full Name and Username */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="fullName" className="text-body-medium text-paragraphContent mb-1">
                      Full Name
                    </label>
                    <Input
                      type="text"
                      id="fullName"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleChange}
                      disabled={isLoading}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">We won't show your full name publicly</p>
                  </div>
                  <div>
                    <label htmlFor="username" className="text-body-medium text-paragraphContent mb-1">
                      Username
                    </label>
                    <div className="flex items-center bg-landingBackground border-0 rounded-md">
                      <span className="text-gray-500 pl-3">@</span>
                      <Input
                        type="text"
                        id="username"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        disabled={isLoading}
                        className="w-full p-3 bg-landingBackground rounded-r-md"
                        required
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">This will be public. You can change it anytime.</p>
                  </div>
                </div>

                {/* Licensed State and Affiliated Organization */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="licensedState" className="text-body-medium text-paragraphContent mb-1">
                      Licensed State
                    </label>
                    <div className="relative">
                      <select
                        id="licensedState"
                        name="licensedState"
                        value={formData.licensedState}
                        onChange={handleChange}
                        className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md appearance-none text-gray-500"
                        required
                      >
                        <option value="">Select a State</option>
                        <option value="AL">Alabama</option>
                        <option value="AK">Alaska</option>
                        {/* Add more states as needed */}
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <ChevronDown className="text-gray-500" />
                      </div>
                    </div>
                  </div>
                  <div>
                    <label htmlFor="affiliatedOrganization" className="text-body-medium text-paragraphContent mb-1">
                      Affiliated Organization
                    </label>
                    <Input
                      type="text"
                      id="affiliatedOrganization"
                      name="affiliatedOrganization"
                      value={formData.affiliatedOrganization}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md"
                    />
                  </div>
                </div>

                {/* Email and Phone */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="email" className="text-body-medium text-paragraphContent mb-1">
                      Email Address
                    </label>
                    <Input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="phone" className="text-body-medium text-paragraphContent mb-1">
                      Phone Number
                    </label>
                    <Input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md"
                      required
                    />
                  </div>
                </div>

                {/* Password */}
                <div>
                  <label htmlFor="password" className="text-body-medium text-paragraphContent mb-1">
                    Create a Password
                  </label>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md pr-10"
                      required
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Your password must be a combination of numbers, letters, symbols, and capitals.</p>
                </div>

                {/* Checkboxes */}
                <div className="space-y-3 mt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="rememberMe"
                      checked={formData.rememberMe}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, rememberMe: checked === true })
                      }
                      className="border-paragraphContent"
                    />
                    <label
                      htmlFor="rememberMe"
                      className="text-body-medium text-paragraphContent"
                    >
                      Remember me on this device
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="signUpForUpdates"
                      checked={formData.signUpForUpdates}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, signUpForUpdates: checked === true })
                      }
                      className="border-paragraphContent"
                    />
                    <label
                      htmlFor="signUpForUpdates"
                      className="text-body-medium text-paragraphContent"
                    >
                      Sign me up for important news and updates
                    </label>
                  </div>
                </div>

                {/* Sign Up Button */}
                <Button
                  type="submit"
                  variant="netural"
                  className="w-full font-normal h-11 rounded-sm"
                  disabled={isLoading}
                >
                  <span className="text-white text-title-medium">{isLoading ? "Registering..." : "Sign Up"}</span>
                </Button>
              </form>
            </div>

            {/* Login link */}
            <div className="text-sm mt-8 pt-8 border-t border-gray-200 mb-5">
              Already have an account? <Link href="/login" className="text-black font-medium underline">Log In</Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
