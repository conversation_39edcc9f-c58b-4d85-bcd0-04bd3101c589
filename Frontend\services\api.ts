import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if it exists
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface ProviderSignupData {
  fullName: string;
  username: string;
  email: string;
  password: string;
  phoneNumber: string;
  licensedState: string;
  affiliatedOrganization: string;
  role: number;
}

export interface PatientSignupData {
  fullName: string;
  username: string;
  email: string;
  password: string;
  phoneNumber: string;
  birthday: string;
  militaryVeteran: boolean;
  role: number;
}

export interface IndividualSignupData {
  fullName: string;
  username: string;
  email: string;
  password: string;
  phoneNumber: string;
  birthday: string;
  role: number;
}

export const authService = {
  login: async (credentials: LoginCredentials) => {
    const response = await api.post('/auth/login', credentials);
    if (response.data.data.token) {
      localStorage.setItem('token', response.data.data.token);
    }
    return response.data;
  },

  registerProvider: async (data: ProviderSignupData) => {
    const response = await api.post('/auth/register', {
      ...data,
      role: 3 // Provider role
    });
    return response.data;
  },

  registerPatient: async (data: PatientSignupData) => {
    const response = await api.post('/auth/register', {
      ...data,
      role: 4 // Patient role
    });
    return response.data;
  },

  registerIndividual: async (data: IndividualSignupData) => {
    const response = await api.post('/auth/register', {
      ...data,
      role: 4 // Patient role (individual users are patients)
    });
    return response.data;
  },

  logout: () => {
    localStorage.removeItem('token');
  },

  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  }
};

export default api; 