"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { isValidEmailOrPhone } from "@/lib/validation";
import { useDispatch } from "react-redux";
import { setUser } from "@/store/userSlice";
import { authService } from "@/services/api";

// Define form validation schema
const loginFormSchema = z.object({
  email: z.string()
    .min(1, { message: "Email is required" })
    .email({ message: "Please enter a valid email address" }),
  password: z.string().min(6, {
    message: "Password must be at least 6 characters",
  }),
  rememberMe: z.boolean().default(false),
});

type LoginFormValues = z.infer<typeof loginFormSchema>;

export default function LoginPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);

  // Initialize form with react-hook-form
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
    mode: "onBlur",
  });

  // Form submission handler
  async function onSubmit(data: LoginFormValues) {
    setIsLoading(true);
    setLoginError(null);

    try {
      const response = await authService.login({
        email: data.email,
        password: data.password
      });

      // Store user data in Redux
      dispatch(setUser(response.data.user));

      // Redirect based on user role
      const redirectPath = response.data.user.role === 3 ? '/provider/dashboard' : '/patient/dashboard';
      router.push(redirectPath);
    } catch (error: any) {
      console.error("Login error:", error);
      setLoginError(error.response?.data?.message || "An error occurred during login. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex flex-col">
      <main className="flex max-md:flex-col container-fluid md:h-[calc(100vh-81px)]">
        {/* Left side - Mountain image */}
        <div className="md:w-1/2 max-md:h-[450px] relative">
          <Image
            src="/auth/login.svg"
            alt="Mountain landscape"
            fill
            className="w-full h-full"
            priority
          />
        </div>

        {/* Right side - Login form */}
        <div className="w-full md:w-1/2 flex items-center max-md:justify-center justify-end">
          <div className="w-full max-w-[560px] lg:pr-20 max-xl:px-5">
            <div className="py-10">
              <h1 className="text-display-md text-paragraphContent max-md:text-center">Log In</h1>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field, fieldState }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel className="text-body-medium text-paragraphContent">Email</FormLabel>
                      <FormControl>
                        <Input
                          className={`bg-landingBackground border-0 border-b-2 ${
                            fieldState.invalid ? 'border-red-500' : 'border-medium-gray'
                          } rounded-none shadow-none h-12 focus-visible:ring-0 focus-visible:border-gray-500 px-2`}
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage className="text-red-500" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field, fieldState }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel className="text-body-medium text-paragraphContent">Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          className={`bg-landingBackground border-0 border-b-2 ${
                            fieldState.invalid ? 'border-red-500' : 'border-medium-gray'
                          } rounded-none shadow-none h-12 focus-visible:ring-0 focus-visible:border-gray-500`}
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage className="text-red-500" />
                    </FormItem>
                  )}
                />

                <div className="flex items-center justify-between pt-1">
                  <FormField
                    control={form.control}
                    name="rememberMe"
                    render={({ field }) => (
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="rememberMe"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                          className="border-neturalDark"
                        />
                        <label
                          htmlFor="rememberMe"
                          className="text-sm font-normal text-neturalDark"
                        >
                          Remember me
                        </label>
                      </div>
                    )}
                  />

                  <Link
                    href="/forgot-password"
                    className="text-body-medium text-darkerBlue"
                  >
                    Forgot Password?
                  </Link>
                </div>

                {loginError && (
                  <div className="text-red-500 text-sm mt-2 bg-red-50 p-2 rounded border border-red-200">
                    {loginError}
                  </div>
                )}

                <div className="pt-2 pb-8">
                  <Button
                    type="submit"
                    variant="netural"
                    className="w-full font-normal h-11 rounded-sm"
                    disabled={isLoading}
                  >
                    {isLoading ? "Logging in..." : "Log In"}
                  </Button>
                </div>
              </form>
            </Form>

            {/* Sign up link */}
            <div className="text-sm text-black border-t border-gray-200 pt-8">
              Don't have an account? <Link href="/signup" className="font-medium underline">Sign Up</Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
