"use client";

import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { PatientDashboard } from "@/components/dashboard/patient/page";
import { ProviderDashboard } from "@/components/dashboard/provider/page";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";

function DashboardContent() {
  const userRole = useSelector((state: RootState) => state.user.user?.role);
  const searchParams = useSearchParams();
  const patientId = searchParams.get("patientId");

  // Handle loading state when userRole is not yet available
  if (!userRole) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (userRole === "patient") {
    return <PatientDashboard patientId={patientId ? parseInt(patientId) : 0} />;
  }

  if (userRole === "provider") {
    return (
      patientId ? (
        <PatientDashboard patientId={patientId ? parseInt(patientId) : 0} />
      ) : (
        <ProviderDashboard />
      )
    );
  }

  // Fallback for unknown user roles
  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-lg">Invalid user role</div>
    </div>
  );
}

export default function Dashboard() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading...</div>
      </div>
    }>
      <DashboardContent />
    </Suspense>
  );
}
