"use client";

import React, { useState, useEffect } from 'react';
import { RootState } from "@/store";
import { useSearchParams } from 'next/navigation';
import { Tab } from '../Tab';
import { HealthStats } from '../patient/HealthStats';
import { useSelector } from "react-redux";
import { Overview } from '../patient/Overview';
import { HeroJourney } from '../patient/HeroJourney';

export function PatientDashboard({ patientId }: { patientId: number }) {
  const userRole = useSelector((state: RootState) => state.user.user?.role);
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState<'overview' | 'program' | 'challenge' | 'health' | 'hero-journey'>('overview');
  const [isMobile, setIsMobile] = useState(false);

  // Handle URL parameters for tab navigation
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab === 'challenges') {
      setActiveTab('challenge');
    } else if (tab === 'health-stats') {
      setActiveTab('health');
    } else if (tab === 'program') {
      setActiveTab('program');
    } else if (tab === 'hero-journey') {
      setActiveTab('hero-journey');
    } else {
      setActiveTab('overview');
    }
  }, [searchParams]);

  useEffect(() => {
    const handleResize = () => {
      if (typeof window !== 'undefined') {
        setIsMobile(window.innerWidth < 768);
      }
    };
    handleResize();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  let tabContent;
  if (activeTab === 'overview') {
    tabContent = (
      <Overview />
    );
  } else if (activeTab === 'program') {
    tabContent = (
      <div>
        {/* TODO: Implement Program Stats widgets here */}
        <div className="text-center text-gray-400 py-20">Program Stats content goes here.</div>
      </div>
    );
  } else if (activeTab === 'challenge') {
    tabContent = (
      <div>
        {/* TODO: Implement Challenge Stats widgets here */}
        <div className="text-center text-gray-400 py-20">Challenge Stats content goes here.</div>
      </div>
    );
  } else if (activeTab === 'health') {
    tabContent = (
      <HealthStats />
    );
  } else if (activeTab === 'hero-journey') {
    tabContent = (
      <HeroJourney />
    );
  }

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 md:mb-6 gap-2">
        <p className="text-3xl md:text-display-md text-paragraphContent font-bold">{ userRole === "patient" ? "My Dashboard" : "Sarah’s Dashboard"}</p>
      </div>
      {/* Tabs */}
      <div className="border-b mb-2 md:mb-4 overflow-x-auto w-full -mx-2 sm:mx-0">
        <div className="flex w-full min-w-max px-2 sm:px-0">
          <Tab
            label="Overview"
            active={activeTab === 'overview'}
            onClick={() => setActiveTab('overview')}
          />
          <Tab
            label="Hero's Journey"
            active={activeTab === 'hero-journey'}
            onClick={() => setActiveTab('hero-journey')}
          />
          <Tab
            label={isMobile ? 'Program' : 'Program Stats'}
            active={activeTab === 'program'}
            onClick={() => setActiveTab('program')}
          />
          <Tab
            label={isMobile ? 'Challenge' : 'Challenge Stats'}
            active={activeTab === 'challenge'}
            onClick={() => setActiveTab('challenge')}
          />
          <Tab
            label={isMobile ? 'Health' : 'Health Stats'}
            active={activeTab === 'health'}
            onClick={() => setActiveTab('health')}
          />
        </div>
      </div>
      {/* Tab Content */}
      <div className="mt-4 md:mt-6 w-full">
        {tabContent}
      </div>
    </div>
  );
}
